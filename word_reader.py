#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取工具
支持读取.docx格式的Word文档内容
"""

from docx import Document
from docx.shared import Inches
import os
import sys
from typing import List, Dict, Any


class WordReader:
    """Word文档读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化Word读取器
        
        Args:
            file_path (str): Word文档路径
        """
        self.file_path = file_path
        self.document = None
        
    def load_document(self) -> bool:
        """
        加载Word文档
        
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"错误: 文件 '{self.file_path}' 不存在")
                return False
                
            if not self.file_path.lower().endswith('.docx'):
                print(f"错误: 文件 '{self.file_path}' 不是.docx格式")
                return False
                
            self.document = Document(self.file_path)
            print(f"成功加载文档: {self.file_path}")
            return True
            
        except Exception as e:
            print(f"加载文档时出错: {str(e)}")
            return False
    
    def get_text_content(self) -> str:
        """
        获取文档的纯文本内容
        
        Returns:
            str: 文档的文本内容
        """
        if not self.document:
            return ""
            
        text_content = []
        for paragraph in self.document.paragraphs:
            if paragraph.text.strip():  # 跳过空段落
                text_content.append(paragraph.text)
                
        return '\n'.join(text_content)
    
    def get_paragraphs(self) -> List[str]:
        """
        获取文档的段落列表
        
        Returns:
            List[str]: 段落文本列表
        """
        if not self.document:
            return []
            
        paragraphs = []
        for paragraph in self.document.paragraphs:
            if paragraph.text.strip():
                paragraphs.append(paragraph.text)
                
        return paragraphs
    
    def get_tables_content(self) -> List[List[List[str]]]:
        """
        获取文档中的表格内容
        
        Returns:
            List[List[List[str]]]: 表格数据，格式为[表格][行][列]
        """
        if not self.document:
            return []
            
        tables_data = []
        for table in self.document.tables:
            table_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    row_data.append(cell.text.strip())
                table_data.append(row_data)
            tables_data.append(table_data)
            
        return tables_data
    
    def get_document_info(self) -> Dict[str, Any]:
        """
        获取文档基本信息
        
        Returns:
            Dict[str, Any]: 文档信息字典
        """
        if not self.document:
            return {}
            
        info = {
            'paragraph_count': len(self.document.paragraphs),
            'table_count': len(self.document.tables),
            'file_path': self.file_path,
            'file_size': os.path.getsize(self.file_path) if os.path.exists(self.file_path) else 0
        }
        
        # 获取文档属性
        core_props = self.document.core_properties
        if core_props:
            info.update({
                'title': core_props.title or '',
                'author': core_props.author or '',
                'subject': core_props.subject or '',
                'created': core_props.created,
                'modified': core_props.modified
            })
            
        return info
    
    def print_document_summary(self):
        """打印文档摘要信息"""
        info = self.get_document_info()
        print("\n=== 文档信息 ===")
        print(f"文件路径: {info.get('file_path', 'N/A')}")
        print(f"文件大小: {info.get('file_size', 0)} 字节")
        print(f"段落数量: {info.get('paragraph_count', 0)}")
        print(f"表格数量: {info.get('table_count', 0)}")
        
        if info.get('title'):
            print(f"标题: {info['title']}")
        if info.get('author'):
            print(f"作者: {info['author']}")
        if info.get('created'):
            print(f"创建时间: {info['created']}")
        if info.get('modified'):
            print(f"修改时间: {info['modified']}")
    
    def save_text_to_file(self, output_path: str) -> bool:
        """
        将文档文本内容保存到文件
        
        Args:
            output_path (str): 输出文件路径
            
        Returns:
            bool: 保存成功返回True
        """
        try:
            text_content = self.get_text_content()
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            print(f"文本内容已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            return False


def main():
    """主函数 - 命令行使用示例"""
    if len(sys.argv) < 2:
        print("使用方法: python word_reader.py <word文档路径> [输出文本文件路径]")
        print("示例: python word_reader.py document.docx output.txt")
        return
    
    word_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 创建Word读取器
    reader = WordReader(word_file)
    
    # 加载文档
    if not reader.load_document():
        return
    
    # 显示文档信息
    reader.print_document_summary()
    
    # 显示文本内容
    print("\n=== 文档内容 ===")
    text_content = reader.get_text_content()
    print(text_content[:500] + "..." if len(text_content) > 500 else text_content)
    
    # 显示表格信息
    tables = reader.get_tables_content()
    if tables:
        print(f"\n=== 表格内容 (共{len(tables)}个表格) ===")
        for i, table in enumerate(tables):
            print(f"\n表格 {i+1}:")
            for row in table[:3]:  # 只显示前3行
                print("  " + " | ".join(row))
            if len(table) > 3:
                print("  ...")
    
    # 保存到文件
    if output_file:
        reader.save_text_to_file(output_file)


if __name__ == "__main__":
    main()
