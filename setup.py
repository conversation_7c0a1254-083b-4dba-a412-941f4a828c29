#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取工具安装脚本
"""

import subprocess
import sys
import os


def install_package(package_name: str, use_conda: bool = False) -> bool:
    """
    安装Python包
    
    Args:
        package_name (str): 包名
        use_conda (bool): 是否使用conda安装
        
    Returns:
        bool: 安装成功返回True
    """
    try:
        if use_conda:
            cmd = ["conda", "install", package_name, "-c", "conda-forge", "-y"]
        else:
            cmd = [sys.executable, "-m", "pip", "install", package_name]
        
        print(f"正在安装 {package_name}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {str(e)}")
        return False


def check_python_environment():
    """检查Python环境"""
    print("=== Python环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")


def install_dependencies():
    """安装依赖包"""
    print("\n=== 安装依赖包 ===")
    
    packages = [
        "python-docx",
        "lxml",  # python-docx的依赖
    ]
    
    # 如果是Windows，尝试安装pywin32
    if sys.platform.startswith('win'):
        packages.append("pywin32")
    
    success_count = 0
    
    for package in packages:
        # 先尝试pip安装
        if install_package(package, use_conda=False):
            success_count += 1
        # 如果pip失败，尝试conda
        elif install_package(package, use_conda=True):
            success_count += 1
    
    print(f"\n安装结果: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✅ 所有依赖安装成功！")
    else:
        print("⚠️ 部分依赖安装失败，但基本功能仍可使用")


def test_installation():
    """测试安装是否成功"""
    print("\n=== 测试安装 ===")
    
    # 测试python-docx
    try:
        from docx import Document
        print("✅ python-docx 可用")
    except ImportError:
        print("❌ python-docx 不可用")
    
    # 测试win32com (仅Windows)
    if sys.platform.startswith('win'):
        try:
            import win32com.client
            print("✅ win32com 可用")
        except ImportError:
            print("❌ win32com 不可用")
    
    # 测试原生方法
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        print("✅ 原生XML解析方法可用")
    except ImportError:
        print("❌ 原生XML解析方法不可用")


def create_sample_document():
    """创建示例Word文档用于测试"""
    print("\n=== 创建示例文档 ===")
    
    try:
        from docx import Document
        
        # 创建新文档
        doc = Document()
        
        # 添加标题
        doc.add_heading('示例Word文档', 0)
        
        # 添加段落
        doc.add_paragraph('这是一个示例Word文档，用于测试Word读取工具。')
        doc.add_paragraph('这个文档包含多个段落和一个表格。')
        
        # 添加表格
        table = doc.add_table(rows=3, cols=2)
        table.style = 'Table Grid'
        
        # 填充表格
        cells = [
            ['姓名', '年龄'],
            ['张三', '25'],
            ['李四', '30']
        ]
        
        for i, row_data in enumerate(cells):
            row = table.rows[i]
            for j, cell_data in enumerate(row_data):
                row.cells[j].text = cell_data
        
        # 保存文档
        sample_file = "sample_document.docx"
        doc.save(sample_file)
        print(f"✅ 示例文档已创建: {sample_file}")
        
        return sample_file
        
    except ImportError:
        print("❌ 无法创建示例文档 (python-docx不可用)")
        return None
    except Exception as e:
        print(f"❌ 创建示例文档时出错: {str(e)}")
        return None


def main():
    """主函数"""
    print("Word文档读取工具 - 环境设置")
    
    # 检查Python环境
    check_python_environment()
    
    # 安装依赖
    install_dependencies()
    
    # 测试安装
    test_installation()
    
    # 创建示例文档
    sample_file = create_sample_document()
    
    # 测试读取功能
    if sample_file:
        print(f"\n=== 测试读取功能 ===")
        
        # 使用原生方法测试
        reader = WordReaderAlternative(sample_file)
        if reader.load_document():
            reader.print_document_summary()
            content = reader.get_text_content()
            print(f"\n文档内容:\n{content}")
    
    print("\n=== 设置完成 ===")
    print("现在你可以使用以下命令读取Word文档:")
    print("  python word_reader.py your_document.docx")
    print("  python word_reader_alternative.py your_document.docx")


if __name__ == "__main__":
    main()
