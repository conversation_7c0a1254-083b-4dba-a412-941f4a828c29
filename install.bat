@echo off
echo Word文档读取工具 - 安装脚本
echo ================================

echo.
echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 正在安装依赖包...
echo.

echo 安装 python-docx...
pip install python-docx
if %errorlevel% neq 0 (
    echo 尝试使用conda安装...
    conda install python-docx -c conda-forge -y
)

echo.
echo 安装 lxml...
pip install lxml
if %errorlevel% neq 0 (
    echo 尝试使用conda安装...
    conda install lxml -c conda-forge -y
)

echo.
echo 安装 pywin32 (Windows专用)...
pip install pywin32
if %errorlevel% neq 0 (
    echo pywin32安装失败，但不影响基本功能
)

echo.
echo 运行设置脚本...
python setup.py

echo.
echo ================================
echo 安装完成！
echo.
echo 使用方法:
echo   python word_reader.py your_document.docx
echo   python word_reader_alternative.py your_document.docx
echo.
pause
