# Word文档读取工具

这是一个用Python编写的Word文档读取工具，提供多种方法来读取.docx格式文档的内容。

## 功能特性

- ✅ 读取.docx格式的Word文档
- ✅ 提取纯文本内容
- ✅ 获取段落列表
- ✅ 提取表格数据
- ✅ 获取文档元信息（标题、作者、创建时间等）
- ✅ 将文本内容保存到文件
- ✅ 支持批量处理多个文档
- ✅ 多种读取方法（python-docx、原生XML解析、win32com）
- ✅ 自动环境检测和依赖安装

## 快速开始

### Windows用户 - 一键安装

```bash
# 双击运行或在命令行执行
install.bat
```

### 手动安装依赖

```bash
# 方法1: 使用pip
pip install python-docx lxml

# 方法2: 使用conda
conda install python-docx lxml -c conda-forge

# Windows用户额外安装（可选）
pip install pywin32
```

### 运行环境设置

```bash
python setup.py
```

## 使用方法

### 1. 命令行使用

```bash
# 方法1: 使用完整功能版本（推荐）
python word_reader.py document.docx
python word_reader.py document.docx output.txt

# 方法2: 使用多方法尝试版本（兼容性更好）
python word_reader_alternative.py document.docx
python word_reader_alternative.py document.docx output.txt
```

### 2. 作为模块使用

```python
from word_reader import WordReader

# 创建读取器
reader = WordReader("your_document.docx")

# 加载文档
if reader.load_document():
    # 获取文本内容
    text = reader.get_text_content()
    print(text)
    
    # 获取段落
    paragraphs = reader.get_paragraphs()
    for para in paragraphs:
        print(f"段落: {para}")
    
    # 获取表格
    tables = reader.get_tables_content()
    for i, table in enumerate(tables):
        print(f"表格 {i+1}:")
        for row in table:
            print("  " + " | ".join(row))
    
    # 获取文档信息
    info = reader.get_document_info()
    print(f"文档信息: {info}")
```

### 3. 运行示例

```bash
python example_usage.py
```

## API 参考

### WordReader 类

#### 方法

- `__init__(file_path: str)` - 初始化读取器
- `load_document() -> bool` - 加载Word文档
- `get_text_content() -> str` - 获取纯文本内容
- `get_paragraphs() -> List[str]` - 获取段落列表
- `get_tables_content() -> List[List[List[str]]]` - 获取表格数据
- `get_document_info() -> Dict[str, Any]` - 获取文档信息
- `print_document_summary()` - 打印文档摘要
- `save_text_to_file(output_path: str) -> bool` - 保存文本到文件

## 注意事项

1. 仅支持.docx格式的Word文档（不支持.doc格式）
2. 如需处理.doc格式，可以先用Word或LibreOffice转换为.docx
3. 表格数据以三维列表形式返回：[表格索引][行索引][列索引]
4. 文档路径支持相对路径和绝对路径

## 错误处理

工具包含完善的错误处理机制：
- 文件不存在检查
- 文件格式验证
- 异常捕获和友好错误信息

## 故障排除

### 常见问题

1. **Python环境问题**
   ```
   Fatal Python error: init_fs_encoding: failed to get the Python codec
   ```
   - 解决方案：重新安装Python或使用conda环境

2. **依赖安装失败**
   ```
   pip install python-docx 失败
   ```
   - 解决方案：尝试使用conda安装或使用原生XML解析方法

3. **文档读取失败**
   - 确保文件是.docx格式（不是.doc）
   - 检查文件是否损坏
   - 尝试使用不同的读取方法

### 多种读取方法

本工具提供三种读取方法：

1. **python-docx方法**（推荐）
   - 功能最完整，支持表格、样式等
   - 需要安装python-docx库

2. **原生XML解析方法**
   - 不依赖外部库，兼容性好
   - 功能相对简单，主要提取文本

3. **win32com方法**（仅Windows）
   - 使用Microsoft Word COM接口
   - 需要安装Microsoft Word和pywin32

## 扩展功能

如需更多功能，可以考虑：
- 提取图片内容
- 处理文档样式信息
- 支持更多文档格式（.doc, .rtf等）
- 添加文本搜索和替换功能
- 支持文档格式转换
