# Word文档读取工具 - 使用指南

## 🚀 快速开始

我为你创建了一个完整的Word文档读取解决方案，包含多种实现方法以确保兼容性。

### 📁 文件说明

1. **word_reader.py** - 主要的Word读取器（使用python-docx库）
2. **word_reader_alternative.py** - 备用读取器（原生XML解析 + 多方法尝试）
3. **word_reader_gui.py** - 图形界面版本
4. **example_usage.py** - 使用示例
5. **test_word_reader.py** - 测试脚本
6. **setup.py** - 环境设置脚本
7. **install.bat** - Windows一键安装脚本
8. **requirements.txt** - 依赖列表

## 🔧 环境设置

### 方法1: 一键安装（Windows推荐）

```bash
# 双击运行或在命令行执行
install.bat
```

### 方法2: 手动安装

```bash
# 安装依赖
pip install python-docx lxml

# 可选：Windows用户安装COM支持
pip install pywin32

# 运行环境测试
python setup.py
```

### 方法3: 如果Python环境有问题

如果遇到Python环境问题，可以：

1. **使用原生方法**（不需要额外依赖）
   ```bash
   python word_reader_alternative.py your_document.docx
   ```

2. **重新配置Python环境**
   - 重新安装Python
   - 或使用Anaconda/Miniconda

## 📖 使用方法

### 命令行使用

```bash
# 基本用法
python word_reader.py document.docx

# 保存到文件
python word_reader.py document.docx output.txt

# 使用备用方法
python word_reader_alternative.py document.docx
```

### 图形界面使用

```bash
python word_reader_gui.py
```

然后：
1. 点击"浏览"选择Word文档
2. 点击"读取"提取内容
3. 查看文档信息和内容
4. 可以保存、复制或清空内容

### 编程使用

```python
from word_reader import WordReader

# 创建读取器
reader = WordReader("your_document.docx")

# 加载并读取
if reader.load_document():
    # 获取文本
    text = reader.get_text_content()
    print(text)
    
    # 获取表格
    tables = reader.get_tables_content()
    for table in tables:
        print(table)
```

## 🎯 功能特性

### ✅ 支持的功能

- 读取.docx格式Word文档
- 提取纯文本内容
- 获取段落列表
- 提取表格数据
- 获取文档元信息
- 保存文本到文件
- 批量处理文档
- 图形界面操作
- 多种读取方法自动切换

### ⚠️ 限制

- 仅支持.docx格式（不支持.doc）
- 不提取图片和复杂格式
- 表格样式信息会丢失

## 🔍 测试

```bash
# 运行测试
python test_word_reader.py

# 查看示例
python example_usage.py
```

## 🆘 故障排除

### 问题1: Python环境错误
```
Fatal Python error: init_fs_encoding
```
**解决方案**: 使用原生方法或重新安装Python

### 问题2: 依赖安装失败
```
pip install 失败
```
**解决方案**: 
- 尝试使用conda: `conda install python-docx -c conda-forge`
- 或使用原生方法（不需要依赖）

### 问题3: 文档读取失败
**解决方案**:
- 确保文件是.docx格式
- 检查文件是否损坏
- 尝试不同的读取方法

## 📞 技术支持

如果遇到问题：

1. 首先尝试使用 `word_reader_alternative.py`（兼容性最好）
2. 检查文档格式是否正确
3. 运行 `python setup.py` 进行环境诊断
4. 查看错误信息并参考故障排除部分

## 🎉 开始使用

1. 将你的Word文档放在项目目录中
2. 运行: `python word_reader_alternative.py your_document.docx`
3. 查看提取的内容
4. 如需图形界面，运行: `python word_reader_gui.py`

现在你就可以轻松读取Word文档了！
