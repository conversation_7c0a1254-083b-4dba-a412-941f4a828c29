#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取器测试脚本
"""

import unittest
import os
import tempfile
from docx import Document
from word_reader import WordReader


class TestWordReader(unittest.TestCase):
    """WordReader测试类"""
    
    def setUp(self):
        """测试前准备 - 创建测试用的Word文档"""
        self.test_file = tempfile.NamedTemporaryFile(suffix='.docx', delete=False)
        self.test_file.close()
        
        # 创建一个简单的测试文档
        doc = Document()
        doc.add_heading('测试文档标题', 0)
        doc.add_paragraph('这是第一段内容。')
        doc.add_paragraph('这是第二段内容，包含一些测试文字。')
        
        # 添加表格
        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = '姓名'
        table.cell(0, 1).text = '年龄'
        table.cell(1, 0).text = '张三'
        table.cell(1, 1).text = '25'
        
        doc.save(self.test_file.name)
        
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.test_file.name):
            os.unlink(self.test_file.name)
    
    def test_load_document_success(self):
        """测试成功加载文档"""
        reader = WordReader(self.test_file.name)
        self.assertTrue(reader.load_document())
        self.assertIsNotNone(reader.document)
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        reader = WordReader("nonexistent.docx")
        self.assertFalse(reader.load_document())
    
    def test_load_wrong_format(self):
        """测试加载错误格式的文件"""
        reader = WordReader("test.txt")
        self.assertFalse(reader.load_document())
    
    def test_get_text_content(self):
        """测试获取文本内容"""
        reader = WordReader(self.test_file.name)
        reader.load_document()
        
        text = reader.get_text_content()
        self.assertIn('测试文档标题', text)
        self.assertIn('第一段内容', text)
        self.assertIn('第二段内容', text)
    
    def test_get_paragraphs(self):
        """测试获取段落"""
        reader = WordReader(self.test_file.name)
        reader.load_document()
        
        paragraphs = reader.get_paragraphs()
        self.assertGreater(len(paragraphs), 0)
        self.assertTrue(any('测试文档标题' in p for p in paragraphs))
    
    def test_get_tables_content(self):
        """测试获取表格内容"""
        reader = WordReader(self.test_file.name)
        reader.load_document()
        
        tables = reader.get_tables_content()
        self.assertEqual(len(tables), 1)  # 应该有一个表格
        
        # 检查表格内容
        table = tables[0]
        self.assertEqual(len(table), 2)  # 2行
        self.assertEqual(len(table[0]), 2)  # 2列
        self.assertEqual(table[0][0], '姓名')
        self.assertEqual(table[1][0], '张三')
    
    def test_get_document_info(self):
        """测试获取文档信息"""
        reader = WordReader(self.test_file.name)
        reader.load_document()
        
        info = reader.get_document_info()
        self.assertIn('paragraph_count', info)
        self.assertIn('table_count', info)
        self.assertIn('file_path', info)
        self.assertEqual(info['table_count'], 1)
    
    def test_save_text_to_file(self):
        """测试保存文本到文件"""
        reader = WordReader(self.test_file.name)
        reader.load_document()
        
        # 创建临时输出文件
        output_file = tempfile.NamedTemporaryFile(suffix='.txt', delete=False)
        output_file.close()
        
        try:
            # 保存文本
            self.assertTrue(reader.save_text_to_file(output_file.name))
            
            # 验证文件内容
            with open(output_file.name, 'r', encoding='utf-8') as f:
                content = f.read()
                self.assertIn('测试文档标题', content)
                
        finally:
            if os.path.exists(output_file.name):
                os.unlink(output_file.name)


def run_manual_test():
    """手动测试函数 - 用于测试真实的Word文档"""
    print("=== 手动测试 ===")
    print("请将一个Word文档放在当前目录下，命名为 'test_document.docx'")
    
    test_file = "test_document.docx"
    if os.path.exists(test_file):
        reader = WordReader(test_file)
        if reader.load_document():
            reader.print_document_summary()
            
            text = reader.get_text_content()
            print(f"\n文档内容预览:\n{text[:300]}...")
            
            # 保存到文本文件
            reader.save_text_to_file("extracted_text.txt")
        else:
            print("无法加载测试文档")
    else:
        print(f"测试文档 '{test_file}' 不存在")


if __name__ == "__main__":
    print("Word文档读取器测试")
    
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行手动测试
    run_manual_test()
