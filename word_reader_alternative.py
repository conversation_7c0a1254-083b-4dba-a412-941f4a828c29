#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取工具 - 多种方法实现
提供多种读取Word文档的方法，包括使用不同的库
"""

import os
import sys
import zipfile
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional


class WordReaderAlternative:
    """
    Word文档读取器 - 使用原生Python库
    不依赖python-docx，直接解析.docx文件的XML结构
    """
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.content = ""
        self.paragraphs = []
        
    def load_document(self) -> bool:
        """
        加载Word文档
        .docx文件实际上是一个ZIP压缩包，包含XML文件
        """
        try:
            if not os.path.exists(self.file_path):
                print(f"错误: 文件 '{self.file_path}' 不存在")
                return False
                
            if not self.file_path.lower().endswith('.docx'):
                print(f"错误: 文件 '{self.file_path}' 不是.docx格式")
                return False
            
            # 使用zipfile读取.docx文件
            with zipfile.ZipFile(self.file_path, 'r') as docx_zip:
                # 读取主文档内容
                try:
                    document_xml = docx_zip.read('word/document.xml')
                    self._parse_document_xml(document_xml)
                    print(f"成功加载文档: {self.file_path}")
                    return True
                except KeyError:
                    print("错误: 无法找到文档内容")
                    return False
                    
        except Exception as e:
            print(f"加载文档时出错: {str(e)}")
            return False
    
    def _parse_document_xml(self, xml_content: bytes):
        """解析文档XML内容"""
        try:
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 定义命名空间
            namespaces = {
                'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main'
            }
            
            # 提取段落文本
            paragraphs = []
            for para in root.findall('.//w:p', namespaces):
                para_text = ""
                for text_elem in para.findall('.//w:t', namespaces):
                    if text_elem.text:
                        para_text += text_elem.text
                
                if para_text.strip():
                    paragraphs.append(para_text.strip())
            
            self.paragraphs = paragraphs
            self.content = '\n'.join(paragraphs)
            
        except Exception as e:
            print(f"解析XML时出错: {str(e)}")
    
    def get_text_content(self) -> str:
        """获取文档的纯文本内容"""
        return self.content
    
    def get_paragraphs(self) -> List[str]:
        """获取文档的段落列表"""
        return self.paragraphs
    
    def get_document_info(self) -> Dict[str, Any]:
        """获取文档基本信息"""
        info = {
            'paragraph_count': len(self.paragraphs),
            'file_path': self.file_path,
            'file_size': os.path.getsize(self.file_path) if os.path.exists(self.file_path) else 0,
            'character_count': len(self.content),
            'word_count': len(self.content.split()) if self.content else 0
        }
        return info
    
    def print_document_summary(self):
        """打印文档摘要信息"""
        info = self.get_document_info()
        print("\n=== 文档信息 ===")
        print(f"文件路径: {info['file_path']}")
        print(f"文件大小: {info['file_size']} 字节")
        print(f"段落数量: {info['paragraph_count']}")
        print(f"字符数量: {info['character_count']}")
        print(f"词数量: {info['word_count']}")
    
    def save_text_to_file(self, output_path: str) -> bool:
        """将文档文本内容保存到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(self.content)
            print(f"文本内容已保存到: {output_path}")
            return True
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            return False


def try_multiple_methods(file_path: str) -> Optional[str]:
    """
    尝试多种方法读取Word文档
    
    Args:
        file_path (str): Word文档路径
        
    Returns:
        Optional[str]: 文档内容，失败返回None
    """
    print(f"尝试读取Word文档: {file_path}")
    
    # 方法1: 使用原生方法
    print("\n方法1: 使用原生XML解析...")
    try:
        reader = WordReaderAlternative(file_path)
        if reader.load_document():
            content = reader.get_text_content()
            if content:
                print("✅ 原生方法成功")
                return content
    except Exception as e:
        print(f"❌ 原生方法失败: {e}")
    
    # 方法2: 尝试使用python-docx
    print("\n方法2: 尝试使用python-docx...")
    try:
        from docx import Document
        doc = Document(file_path)
        paragraphs = []
        for para in doc.paragraphs:
            if para.text.strip():
                paragraphs.append(para.text)
        content = '\n'.join(paragraphs)
        if content:
            print("✅ python-docx方法成功")
            return content
    except ImportError:
        print("❌ python-docx未安装")
    except Exception as e:
        print(f"❌ python-docx方法失败: {e}")
    
    # 方法3: 使用win32com (仅Windows)
    print("\n方法3: 尝试使用win32com...")
    try:
        import win32com.client
        word_app = win32com.client.Dispatch("Word.Application")
        word_app.Visible = False
        doc = word_app.Documents.Open(os.path.abspath(file_path))
        content = doc.Content.Text
        doc.Close()
        word_app.Quit()
        if content:
            print("✅ win32com方法成功")
            return content
    except ImportError:
        print("❌ win32com未安装")
    except Exception as e:
        print(f"❌ win32com方法失败: {e}")
    
    print("❌ 所有方法都失败了")
    return None


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python word_reader_alternative.py <word文档路径> [输出文本文件路径]")
        print("示例:")
        print("  python word_reader_alternative.py document.docx")
        print("  python word_reader_alternative.py document.docx output.txt")
        return
    
    word_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 尝试读取文档
    content = try_multiple_methods(word_file)
    
    if content:
        print(f"\n=== 文档内容预览 ===")
        preview = content[:500] + "..." if len(content) > 500 else content
        print(preview)
        
        # 保存到文件
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"\n✅ 内容已保存到: {output_file}")
            except Exception as e:
                print(f"❌ 保存失败: {e}")
        
        # 显示统计信息
        print(f"\n=== 统计信息 ===")
        print(f"字符数: {len(content)}")
        print(f"词数: {len(content.split())}")
        print(f"行数: {len(content.split(chr(10)))}")
        
    else:
        print("\n❌ 无法读取文档内容")
        print("\n建议:")
        print("1. 确保文件是.docx格式")
        print("2. 安装python-docx: pip install python-docx")
        print("3. 如果是Windows，可以安装pywin32: pip install pywin32")


if __name__ == "__main__":
    main()
