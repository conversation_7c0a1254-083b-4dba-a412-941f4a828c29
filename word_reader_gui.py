#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取工具 - GUI版本
使用tkinter创建图形界面
"""

import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext, ttk
import os
import threading
from word_reader_alternative import WordReaderAlternative, try_multiple_methods


class WordReaderGUI:
    """Word文档读取器GUI"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Word文档读取工具")
        self.root.geometry("800x600")
        
        self.current_file = None
        self.current_content = ""
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, text="Word文档:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)
        ttk.Button(file_frame, text="读取", command=self.read_document).grid(row=0, column=3, padx=(5, 0))
        
        # 信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="文档信息", padding="5")
        info_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.info_text = tk.Text(info_frame, height=4, width=80)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 内容显示区域
        content_frame = ttk.LabelFrame(main_frame, text="文档内容", padding="5")
        content_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.content_text = scrolledtext.ScrolledText(content_frame, height=20, width=80)
        self.content_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="保存文本", command=self.save_text).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="清空", command=self.clear_content).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(button_frame, text="复制内容", command=self.copy_content).grid(row=0, column=2, padx=(0, 5))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)
    
    def browse_file(self):
        """浏览选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择Word文档",
            filetypes=[("Word文档", "*.docx"), ("所有文件", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
    
    def read_document(self):
        """读取文档"""
        file_path = self.file_path_var.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请先选择Word文档")
            return
        
        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return
        
        # 在后台线程中读取文档
        self.progress.start()
        thread = threading.Thread(target=self._read_document_thread, args=(file_path,))
        thread.daemon = True
        thread.start()
    
    def _read_document_thread(self, file_path):
        """在后台线程中读取文档"""
        try:
            # 尝试读取文档
            content = try_multiple_methods(file_path)
            
            # 在主线程中更新UI
            self.root.after(0, self._update_ui_after_read, file_path, content)
            
        except Exception as e:
            self.root.after(0, self._show_error, f"读取文档时出错: {str(e)}")
    
    def _update_ui_after_read(self, file_path, content):
        """读取完成后更新UI"""
        self.progress.stop()
        
        if content:
            self.current_file = file_path
            self.current_content = content
            
            # 更新信息显示
            self._update_info_display(file_path, content)
            
            # 更新内容显示
            self.content_text.delete(1.0, tk.END)
            self.content_text.insert(1.0, content)
            
            messagebox.showinfo("成功", "文档读取成功！")
        else:
            messagebox.showerror("错误", "无法读取文档内容")
    
    def _update_info_display(self, file_path, content):
        """更新信息显示"""
        self.info_text.delete(1.0, tk.END)
        
        # 计算统计信息
        file_size = os.path.getsize(file_path)
        char_count = len(content)
        word_count = len(content.split())
        line_count = len(content.split('\n'))
        
        info = f"文件: {os.path.basename(file_path)}\n"
        info += f"大小: {file_size} 字节\n"
        info += f"字符数: {char_count} | 词数: {word_count} | 行数: {line_count}"
        
        self.info_text.insert(1.0, info)
    
    def _show_error(self, error_msg):
        """显示错误信息"""
        self.progress.stop()
        messagebox.showerror("错误", error_msg)
    
    def save_text(self):
        """保存文本到文件"""
        if not self.current_content:
            messagebox.showwarning("警告", "没有可保存的内容")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存文本文件",
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.current_content)
                messagebox.showinfo("成功", f"文本已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")
    
    def clear_content(self):
        """清空内容"""
        self.content_text.delete(1.0, tk.END)
        self.info_text.delete(1.0, tk.END)
        self.file_path_var.set("")
        self.current_file = None
        self.current_content = ""
    
    def copy_content(self):
        """复制内容到剪贴板"""
        if self.current_content:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.current_content)
            messagebox.showinfo("成功", "内容已复制到剪贴板")
        else:
            messagebox.showwarning("警告", "没有可复制的内容")


def main():
    """主函数"""
    root = tk.Tk()
    app = WordReaderGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
