#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档读取器使用示例
"""

from word_reader import WordReader


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 替换为你的Word文档路径
    word_file = "example.docx"  # 请替换为实际的文档路径
    
    # 创建读取器
    reader = WordReader(word_file)
    
    # 加载文档
    if reader.load_document():
        # 获取文档信息
        reader.print_document_summary()
        
        # 获取文本内容
        text = reader.get_text_content()
        print(f"\n文档文本内容 (前200字符):\n{text[:200]}...")
        
        # 获取段落
        paragraphs = reader.get_paragraphs()
        print(f"\n段落数量: {len(paragraphs)}")
        if paragraphs:
            print(f"第一段: {paragraphs[0][:100]}...")
        
        # 获取表格
        tables = reader.get_tables_content()
        if tables:
            print(f"\n表格数量: {len(tables)}")
            print("第一个表格的内容:")
            for row in tables[0][:3]:  # 显示前3行
                print("  " + " | ".join(row))


def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 处理多个Word文档
    word_files = [
        "document1.docx",
        "document2.docx", 
        "document3.docx"
    ]
    
    for word_file in word_files:
        print(f"\n处理文档: {word_file}")
        reader = WordReader(word_file)
        
        if reader.load_document():
            # 提取文本并保存
            output_file = word_file.replace('.docx', '.txt')
            reader.save_text_to_file(output_file)
        else:
            print(f"跳过文档: {word_file}")


def example_content_analysis():
    """内容分析示例"""
    print("\n=== 内容分析示例 ===")
    
    word_file = "example.docx"  # 请替换为实际的文档路径
    reader = WordReader(word_file)
    
    if reader.load_document():
        # 获取所有段落
        paragraphs = reader.get_paragraphs()
        
        # 统计信息
        total_chars = sum(len(p) for p in paragraphs)
        total_words = sum(len(p.split()) for p in paragraphs)
        
        print(f"总段落数: {len(paragraphs)}")
        print(f"总字符数: {total_chars}")
        print(f"总词数: {total_words}")
        print(f"平均段落长度: {total_chars / len(paragraphs):.1f} 字符" if paragraphs else "无段落")
        
        # 查找包含特定关键词的段落
        keyword = "重要"  # 可以修改为你要搜索的关键词
        matching_paragraphs = [p for p in paragraphs if keyword in p]
        
        if matching_paragraphs:
            print(f"\n包含'{keyword}'的段落:")
            for i, para in enumerate(matching_paragraphs[:3], 1):  # 显示前3个
                print(f"{i}. {para[:100]}...")


if __name__ == "__main__":
    print("Word文档读取器使用示例")
    print("请确保已安装依赖: pip install python-docx")
    print("请将示例中的文件路径替换为实际的Word文档路径")
    
    # 运行示例
    example_basic_usage()
    # example_batch_processing()  # 取消注释以运行批量处理示例
    # example_content_analysis()  # 取消注释以运行内容分析示例
